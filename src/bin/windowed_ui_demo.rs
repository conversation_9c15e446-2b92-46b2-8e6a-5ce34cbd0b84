//! Windowed UI Demo with Label Components and Asset Fonts
//!
//! This demo creates a window and renders various UI components including
//! Label nodes with fonts loaded from the assets directory, demonstrating
//! the modernized ab_glyph text rendering system with baseline alignment.

use std::sync::Arc;
use winit::{
    application::ApplicationHandler,
    event::{WindowEvent, ElementState},
    event_loop::{EventLoop, ActiveEventLoop},
    window::{Window, WindowId, WindowAttributes},
    keyboard::{KeyCode, PhysicalKey},
};
use wgpu::{Surface, Device, Queue, SurfaceConfiguration};
use verturion::core::scene::nodes::ui::Label;
use verturion::core::scene::nodes::ui::label::TextAlign;
use verturion::core::variant::{Color, String as GodotString};
use verturion::core::math::Vector2;
use verturion::core::renderer::ui_renderer::UIRenderer;

/// ### Windowed UI demonstration application.
pub struct WindowedUIDemo {
    /// Graphics device
    device: Option<Device>,
    /// Command queue
    queue: Option<Queue>,
    /// Window surface
    surface: Option<Surface<'static>>,
    /// Surface configuration
    surface_config: Option<SurfaceConfiguration>,
    /// UI renderer with ab_glyph backend
    ui_renderer: Option<UIRenderer>,
    /// Test labels for different scenarios
    test_labels: Vec<Label>,
    /// Current font index for switching
    current_font_index: usize,
    /// Available fonts for testing
    available_fonts: Vec<String>,
    /// Window size
    window_size: Vector2,
    /// Window reference
    window: Option<Arc<Window>>,
}

impl Default for WindowedUIDemo {
    fn default() -> Self {
        Self::new()
    }
}

impl WindowedUIDemo {
    /// ### Creates a new windowed UI demo.
    pub fn new() -> Self {
        // Define available fonts from assets directory
        let available_fonts = vec![
            "assets/fonts/Roboto-Regular.ttf".to_string(),
            "assets/fonts/Roboto-Bold.ttf".to_string(),
            "assets/fonts/DejaVuSansMono.ttf".to_string(),
            "assets/fonts/Coolvetica Rg.otf".to_string(),
            "assets/fonts/Roboto-Light.ttf".to_string(),
            "assets/fonts/Roboto-Medium.ttf".to_string(),
        ];

        println!("🎯 Windowed UI Demo Initializing");
        println!("=================================");
        println!("Available fonts: {}", available_fonts.len());
        for (i, font) in available_fonts.iter().enumerate() {
            println!("  {}. {}", i + 1, font);
        }
        println!();

        Self {
            device: None,
            queue: None,
            surface: None,
            surface_config: None,
            ui_renderer: None,
            test_labels: Vec::new(),
            current_font_index: 0,
            available_fonts,
            window_size: Vector2::new(800.0, 600.0),
            window: None,
        }
    }

    /// ### Creates comprehensive test labels for UI demonstration.
    fn create_ui_labels(&mut self) {
        let mut labels = Vec::new();
        let mut y_position = 30.0;
        let line_height = 45.0;

        // 1. Title Label
        let mut title_label = Label::new("TitleLabel");
        title_label.set_text(GodotString::from("🎯 Verturion UI Demo - ab_glyph Modernization"));
        title_label.set_font_size(24);
        title_label.set_font_color(Color::new(1.0, 1.0, 0.0, 1.0)); // Yellow
        title_label.set_text_align(TextAlign::Center);
        title_label.base_mut().set_position(Vector2::new(50.0, y_position));
        title_label.set_size(Vector2::new(700.0, 35.0));
        labels.push(title_label);
        y_position += line_height;

        // 2. Baseline Test Label
        let mut baseline_label = Label::new("BaselineTest");
        baseline_label.set_text(GodotString::from("Baseline Test: gjpqy HELLO World 123"));
        baseline_label.set_font_size(20);
        baseline_label.set_font_color(Color::new(0.8, 1.0, 0.8, 1.0)); // Light green
        baseline_label.base_mut().set_position(Vector2::new(50.0, y_position));
        baseline_label.set_size(Vector2::new(700.0, 30.0));
        labels.push(baseline_label);
        y_position += line_height;

        // 3. Mixed Case Label
        let mut mixed_label = Label::new("MixedCaseTest");
        mixed_label.set_text(GodotString::from("MiXeD CaSe: Shorthop bdfhklt"));
        mixed_label.set_font_size(18);
        mixed_label.set_font_color(Color::new(1.0, 0.8, 0.8, 1.0)); // Light red
        mixed_label.base_mut().set_position(Vector2::new(50.0, y_position));
        mixed_label.set_size(Vector2::new(700.0, 30.0));
        labels.push(mixed_label);
        y_position += line_height;

        // 4. Special Characters Label
        let mut special_label = Label::new("SpecialCharsTest");
        special_label.set_text(GodotString::from("Special: @#$%^&*()_+-=[]{}|;':\",./<>?"));
        special_label.set_font_size(16);
        special_label.set_font_color(Color::new(0.8, 0.8, 1.0, 1.0)); // Light blue
        special_label.base_mut().set_position(Vector2::new(50.0, y_position));
        special_label.set_size(Vector2::new(700.0, 30.0));
        labels.push(special_label);
        y_position += line_height;

        // 5. Numbers and Punctuation Label
        let mut numbers_label = Label::new("NumbersTest");
        numbers_label.set_text(GodotString::from("Numbers: 0123456789 .,;:!?"));
        numbers_label.set_font_size(16);
        numbers_label.set_font_color(Color::new(1.0, 0.8, 1.0, 1.0)); // Light magenta
        numbers_label.base_mut().set_position(Vector2::new(50.0, y_position));
        numbers_label.set_size(Vector2::new(700.0, 30.0));
        labels.push(numbers_label);
        y_position += line_height;

        // 6. Large Font Label
        let mut large_label = Label::new("LargeFontTest");
        large_label.set_text(GodotString::from("Large Font - 28px"));
        large_label.set_font_size(28);
        large_label.set_font_color(Color::new(1.0, 1.0, 1.0, 1.0)); // White
        large_label.base_mut().set_position(Vector2::new(50.0, y_position));
        large_label.set_size(Vector2::new(700.0, 35.0));
        labels.push(large_label);
        y_position += 50.0;

        // 7. Small Font Label
        let mut small_label = Label::new("SmallFontTest");
        small_label.set_text(GodotString::from("Small Font - 12px for detailed text"));
        small_label.set_font_size(12);
        small_label.set_font_color(Color::new(0.9, 0.9, 0.9, 1.0)); // Light gray
        small_label.base_mut().set_position(Vector2::new(50.0, y_position));
        small_label.set_size(Vector2::new(700.0, 25.0));
        labels.push(small_label);
        y_position += 40.0;

        // 8. Instructions Label
        let mut instructions_label = Label::new("InstructionsLabel");
        instructions_label.set_text(GodotString::from("Press F1-F6 to switch fonts, ESC to exit"));
        instructions_label.set_font_size(14);
        instructions_label.set_font_color(Color::new(0.7, 0.7, 0.7, 1.0)); // Gray
        instructions_label.set_text_align(TextAlign::Center);
        instructions_label.base_mut().set_position(Vector2::new(50.0, y_position));
        instructions_label.set_size(Vector2::new(700.0, 25.0));
        labels.push(instructions_label);

        // Set initial font for all labels
        let current_font = &self.available_fonts[self.current_font_index];
        for label in &mut labels {
            label.set_font(Some(current_font.clone()));
        }

        self.test_labels = labels;
        println!("✅ Created {} UI labels with font: {}", self.test_labels.len(), current_font);
    }

    /// ### Switches to the specified font index.
    fn switch_font(&mut self, font_index: usize) {
        if font_index < self.available_fonts.len() {
            self.current_font_index = font_index;
            let font_name = &self.available_fonts[font_index];
            
            println!("🔤 Switching to font {}: {}", font_index + 1, font_name);
            
            // Update all labels to use the new font
            for label in &mut self.test_labels {
                label.set_font(Some(font_name.clone()));
            }
            
            println!("✅ Font switched successfully - {} labels updated", self.test_labels.len());
        }
    }

    /// ### Handles keyboard input for font switching and controls.
    fn handle_keyboard_input(&mut self, physical_key: PhysicalKey, state: ElementState) {
        if state == ElementState::Pressed {
            match physical_key {
                PhysicalKey::Code(KeyCode::F1) => self.switch_font(0),
                PhysicalKey::Code(KeyCode::F2) => self.switch_font(1),
                PhysicalKey::Code(KeyCode::F3) => self.switch_font(2),
                PhysicalKey::Code(KeyCode::F4) => self.switch_font(3),
                PhysicalKey::Code(KeyCode::F5) => self.switch_font(4),
                PhysicalKey::Code(KeyCode::F6) => self.switch_font(5),
                _ => {}
            }
        }
    }

    /// ### Initializes the graphics system for the window.
    async fn init_graphics(&mut self, window: Arc<Window>) -> Result<(), Box<dyn std::error::Error>> {
        let window_size = window.inner_size();
        self.window_size = Vector2::new(window_size.width as f32, window_size.height as f32);

        // Initialize wgpu
        let instance = wgpu::Instance::new(wgpu::InstanceDescriptor {
            backends: wgpu::Backends::all(),
            dx12_shader_compiler: Default::default(),
            flags: wgpu::InstanceFlags::default(),
            gles_minor_version: wgpu::Gles3MinorVersion::Automatic,
        });

        let surface = instance.create_surface(window.clone())?;

        let adapter = instance
            .request_adapter(&wgpu::RequestAdapterOptions {
                power_preference: wgpu::PowerPreference::default(),
                compatible_surface: Some(&surface),
                force_fallback_adapter: false,
            })
            .await
            .ok_or("Failed to find an appropriate adapter")?;

        let (device, queue) = adapter
            .request_device(
                &wgpu::DeviceDescriptor {
                    label: None,
                    required_features: wgpu::Features::empty(),
                    required_limits: wgpu::Limits::default(),
                    memory_hints: wgpu::MemoryHints::default(),
                },
                None,
            )
            .await?;

        let surface_caps = surface.get_capabilities(&adapter);
        let surface_format = surface_caps
            .formats
            .iter()
            .copied()
            .find(|f| f.is_srgb())
            .unwrap_or(surface_caps.formats[0]);

        let surface_config = SurfaceConfiguration {
            usage: wgpu::TextureUsages::RENDER_ATTACHMENT,
            format: surface_format,
            width: window_size.width,
            height: window_size.height,
            present_mode: surface_caps.present_modes[0],
            alpha_mode: surface_caps.alpha_modes[0],
            view_formats: vec![],
            desired_maximum_frame_latency: 2,
        };

        surface.configure(&device, &surface_config);

        // Create UI renderer with ab_glyph backend
        let ui_renderer = UIRenderer::new(&device, &queue, surface_format).await?;

        // Store everything
        self.device = Some(device);
        self.queue = Some(queue);
        self.surface = Some(surface);
        self.surface_config = Some(surface_config);
        self.ui_renderer = Some(ui_renderer);
        self.window = Some(window);

        // Create UI labels now that graphics are initialized
        self.create_ui_labels();

        println!("✅ Graphics system initialized successfully");
        println!("📱 Window size: {}x{}", self.window_size.x, self.window_size.y);

        Ok(())
    }

    /// ### Renders a single frame.
    fn render(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let surface = self.surface.as_ref().ok_or("Surface not initialized")?;
        let device = self.device.as_ref().ok_or("Device not initialized")?;
        let queue = self.queue.as_ref().ok_or("Queue not initialized")?;
        let ui_renderer = self.ui_renderer.as_mut().ok_or("UI renderer not initialized")?;

        let output = surface.get_current_texture()?;
        let view = output.texture.create_view(&wgpu::TextureViewDescriptor::default());

        let mut encoder = device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
            label: Some("Render Encoder"),
        });

        // Clear the screen with dark background
        {
            let _render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("Clear Pass"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view: &view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: wgpu::LoadOp::Clear(wgpu::Color {
                            r: 0.1,
                            g: 0.1,
                            b: 0.15,
                            a: 1.0,
                        }),
                        store: wgpu::StoreOp::Store,
                    },
                })],
                depth_stencil_attachment: None,
                occlusion_query_set: None,
                timestamp_writes: None,
            });
        }

        // Clear UI renderer
        ui_renderer.clear();

        // Render all test labels using the modernized ab_glyph system
        for label in &self.test_labels {
            let position = label.base().get_position();

            // Render text using the UIRenderer with baseline alignment fix
            ui_renderer.render_text(
                label.get_text().as_str(),
                position,
                label.get_font_color(),
                label.get_font_size() as f32,
            )?;
        }

        // Flush UI rendering commands
        {
            let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("UI Render Pass"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view: &view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: wgpu::LoadOp::Load,
                        store: wgpu::StoreOp::Store,
                    },
                })],
                depth_stencil_attachment: None,
                occlusion_query_set: None,
                timestamp_writes: None,
            });

            ui_renderer.flush(&mut render_pass, queue, self.window_size)?;
        }

        queue.submit(std::iter::once(encoder.finish()));
        output.present();

        Ok(())
    }

    /// ### Handles window resize events.
    fn resize(&mut self, new_size: winit::dpi::PhysicalSize<u32>) {
        if new_size.width > 0 && new_size.height > 0 {
            self.window_size = Vector2::new(new_size.width as f32, new_size.height as f32);

            if let (Some(surface), Some(device), Some(surface_config)) =
                (self.surface.as_ref(), self.device.as_ref(), self.surface_config.as_mut()) {
                surface_config.width = new_size.width;
                surface_config.height = new_size.height;
                surface.configure(device, surface_config);

                println!("📱 Window resized to: {}x{}", new_size.width, new_size.height);
            }
        }
    }
}

impl ApplicationHandler for WindowedUIDemo {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        let window_attributes = WindowAttributes::default()
            .with_title("🎯 Verturion UI Demo - ab_glyph Modernization")
            .with_inner_size(winit::dpi::LogicalSize::new(800, 600));

        let window = Arc::new(event_loop.create_window(window_attributes).unwrap());

        println!("🪟 Window created successfully");

        // Initialize graphics system asynchronously
        let window_clone = window.clone();
        let init_result = pollster::block_on(async {
            self.init_graphics(window_clone).await
        });

        match init_result {
            Ok(()) => {
                println!("🎉 Windowed UI Demo ready!");
                println!("⌨️  Controls:");
                println!("   F1-F6: Switch between fonts");
                println!("   ESC: Exit application");
                println!();
            }
            Err(e) => {
                eprintln!("❌ Failed to initialize graphics: {}", e);
            }
        }
    }

    fn window_event(&mut self, event_loop: &ActiveEventLoop, _window_id: WindowId, event: WindowEvent) {
        match event {
            WindowEvent::CloseRequested => {
                println!("👋 Closing Windowed UI Demo");
                event_loop.exit();
            }
            WindowEvent::KeyboardInput { event, .. } => {
                if event.state == ElementState::Pressed {
                    match event.physical_key {
                        PhysicalKey::Code(KeyCode::Escape) => {
                            println!("👋 Exiting Windowed UI Demo");
                            event_loop.exit();
                        }
                        key => {
                            self.handle_keyboard_input(key, event.state);
                        }
                    }
                }
            }
            WindowEvent::Resized(physical_size) => {
                self.resize(physical_size);
            }
            WindowEvent::RedrawRequested => {
                if let Err(e) = self.render() {
                    eprintln!("Render error: {}", e);
                }
            }
            _ => {}
        }
    }

    fn about_to_wait(&mut self, _event_loop: &ActiveEventLoop) {
        if let Some(window) = &self.window {
            window.request_redraw();
        }
    }
}

/// ### Main function - Entry point for the windowed UI demo.
fn main() -> Result<(), Box<dyn std::error::Error>> {
    env_logger::init();

    println!("🎯 Verturion Windowed UI Demo");
    println!("=============================");
    println!("Demonstrating ab_glyph modernization with visual UI components");
    println!("Features:");
    println!("• Label nodes with asset fonts");
    println!("• Baseline alignment validation");
    println!("• Interactive font switching");
    println!("• Real-time rendering");
    println!();

    let event_loop = EventLoop::new()?;
    let mut app = WindowedUIDemo::new();

    event_loop.run_app(&mut app)?;

    Ok(())
}
