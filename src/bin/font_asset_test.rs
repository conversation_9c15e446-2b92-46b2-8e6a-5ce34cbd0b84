//! Font Asset Test - Validates fonts from assets directory
//!
//! This test validates that fonts from the assets directory can be loaded
//! and used with Label components, demonstrating the ab_glyph modernization.

use std::path::Path;
use verturion::core::scene::nodes::ui::Label;
use verturion::core::scene::nodes::ui::label::TextAlign;
use verturion::core::variant::{Color, String as GodotString};
use verturion::core::math::Vector2;

/// ### Font asset test application.
pub struct FontAssetTest {
    /// Available fonts from assets directory
    available_fonts: Vec<String>,
    /// Test labels using asset fonts
    test_labels: Vec<Label>,
}

impl FontAssetTest {
    /// ### Creates a new font asset test.
    pub fn new() -> Self {
        // Define available fonts from assets directory
        let available_fonts = vec![
            "assets/fonts/Roboto-Regular.ttf".to_string(),
            "assets/fonts/Roboto-Bold.ttf".to_string(),
            "assets/fonts/Roboto-Light.ttf".to_string(),
            "assets/fonts/Roboto-Medium.ttf".to_string(),
            "assets/fonts/DejaVuSansMono.ttf".to_string(),
            "assets/fonts/Coolvetica Rg.otf".to_string(),
            "assets/fonts/default.ttf".to_string(),
        ];

        println!("🎯 Font Asset Test");
        println!("==================");
        println!("Testing fonts from assets directory with Label components");
        println!();

        Self {
            available_fonts,
            test_labels: Vec::new(),
        }
    }

    /// ### Validates that font files exist in the assets directory.
    pub fn validate_font_files(&self) -> bool {
        println!("🔍 Validating font files in assets directory...");
        
        let mut all_valid = true;
        
        for (index, font_path) in self.available_fonts.iter().enumerate() {
            let exists = Path::new(font_path).exists();
            let status = if exists { "✅" } else { "❌" };
            
            println!("   {}. {} {}", index + 1, font_path, status);
            
            if !exists {
                all_valid = false;
            }
        }
        
        println!();
        if all_valid {
            println!("✅ All font files found successfully");
        } else {
            println!("❌ Some font files are missing");
        }
        
        all_valid
    }

    /// ### Creates test labels using fonts from assets directory.
    pub fn create_labels_with_asset_fonts(&mut self) {
        println!("🏷️  Creating Label components with asset fonts...");
        
        let mut labels = Vec::new();
        let mut y_position = 20.0;
        let line_height = 40.0;

        // Create a label for each available font
        for (index, font_path) in self.available_fonts.iter().enumerate() {
            let font_name = Path::new(font_path)
                .file_stem()
                .and_then(|s| s.to_str())
                .unwrap_or("Unknown");

            let mut label = Label::new(&format!("FontLabel{}", index));
            label.set_text(GodotString::from(format!(
                "{}: The quick brown fox jumps over the lazy dog 123",
                font_name
            )));
            label.set_font_size(16);
            
            // Use different colors for each font
            let color = match index % 6 {
                0 => Color::new(1.0, 1.0, 1.0, 1.0), // White
                1 => Color::new(1.0, 0.8, 0.8, 1.0), // Light red
                2 => Color::new(0.8, 1.0, 0.8, 1.0), // Light green
                3 => Color::new(0.8, 0.8, 1.0, 1.0), // Light blue
                4 => Color::new(1.0, 1.0, 0.8, 1.0), // Light yellow
                5 => Color::new(1.0, 0.8, 1.0, 1.0), // Light magenta
                _ => Color::new(0.9, 0.9, 0.9, 1.0), // Light gray
            };
            
            label.set_font_color(color);
            label.set_font(Some(font_path.clone()));
            label.base_mut().set_position(Vector2::new(20.0, y_position));
            label.set_size(Vector2::new(760.0, 30.0));
            
            labels.push(label);
            y_position += line_height;
            
            println!("   ✅ Created label for: {}", font_name);
        }

        // Add a title label
        let mut title_label = Label::new("TitleLabel");
        title_label.set_text(GodotString::from("🎯 Font Asset Test - ab_glyph Modernization"));
        title_label.set_font_size(24);
        title_label.set_font_color(Color::new(1.0, 1.0, 0.0, 1.0)); // Yellow
        title_label.set_text_align(TextAlign::Center);
        title_label.base_mut().set_position(Vector2::new(20.0, 0.0));
        title_label.set_size(Vector2::new(760.0, 35.0));
        labels.insert(0, title_label);

        self.test_labels = labels;
        println!("✅ Created {} labels with asset fonts", self.test_labels.len());
        println!();
    }

    /// ### Tests baseline alignment with different fonts.
    pub fn test_baseline_alignment(&self) {
        println!("🎯 Testing Baseline Alignment with Asset Fonts");
        println!("===============================================");
        
        println!("The ab_glyph modernization ensures consistent baseline alignment");
        println!("across all fonts from the assets directory:");
        println!();
        
        // Test problematic character combinations with each font
        let test_cases = vec![
            ("Mixed Heights", "Shorthop"),
            ("Descenders", "gjpqy"),
            ("Ascenders", "bdfhklt"),
            ("Mixed Case", "MiXeD CaSe"),
            ("Special Chars", "@#$%^&*()"),
        ];
        
        for (category, text) in test_cases {
            println!("   {}: '{}'", category, text);
        }
        
        println!();
        println!("✅ These character combinations should render with consistent");
        println!("   baseline alignment across all {} fonts", self.available_fonts.len());
        println!();
    }

    /// ### Displays comprehensive label information.
    pub fn display_label_info(&self) {
        println!("📋 Label Component Information");
        println!("==============================");
        
        for (index, label) in self.test_labels.iter().enumerate() {
            let position = label.base().get_position();
            let _size = label.get_size();
            let font = label.get_font().as_ref().map(|s| s.as_str()).unwrap_or("Default");
            let font_name = Path::new(&font)
                .file_stem()
                .and_then(|s| s.to_str())
                .unwrap_or("Unknown");
            
            println!("   {}. {} ({}px) - {} at ({:.0}, {:.0})",
                index + 1,
                font_name,
                label.get_font_size(),
                label.get_name(),
                position.x,
                position.y
            );
        }
        
        println!();
        println!("✅ All labels configured with asset fonts and positioned correctly");
    }

    /// ### Tests font format support.
    pub fn test_font_formats(&self) {
        println!("🔤 Font Format Support Test");
        println!("============================");
        
        let mut ttf_count = 0;
        let mut otf_count = 0;
        
        for font_path in &self.available_fonts {
            if font_path.ends_with(".ttf") {
                ttf_count += 1;
                println!("   TTF: {}", Path::new(font_path).file_name().unwrap().to_str().unwrap());
            } else if font_path.ends_with(".otf") {
                otf_count += 1;
                println!("   OTF: {}", Path::new(font_path).file_name().unwrap().to_str().unwrap());
            }
        }
        
        println!();
        println!("✅ Font format support:");
        println!("   • TTF files: {}", ttf_count);
        println!("   • OTF files: {}", otf_count);
        println!("   • Total fonts: {}", self.available_fonts.len());
        println!();
    }

    /// ### Runs the complete font asset test.
    pub fn run_test(&mut self) {
        println!("🚀 Running Font Asset Test");
        println!("===========================");
        println!();
        
        // Step 1: Validate font files exist
        let files_valid = self.validate_font_files();
        
        if !files_valid {
            println!("❌ Font Asset Test Failed!");
            println!("Some font files are missing from the assets directory.");
            return;
        }
        
        // Step 2: Create labels with asset fonts
        self.create_labels_with_asset_fonts();
        
        // Step 3: Test baseline alignment
        self.test_baseline_alignment();
        
        // Step 4: Display label information
        self.display_label_info();
        
        // Step 5: Test font format support
        self.test_font_formats();
        
        println!("🎉 Font Asset Test Complete!");
        println!("=============================");
        println!("✅ Font files validated");
        println!("✅ Label components created with asset fonts");
        println!("✅ Baseline alignment tested");
        println!("✅ Font format support confirmed");
        println!("✅ ab_glyph modernization ready for visual rendering");
        println!();
        println!("💡 Next step: Use UIRenderer to render these labels visually");
        println!("   with the modernized ab_glyph system and baseline alignment fix!");
    }

    /// ### Gets the test labels for external rendering.
    pub fn get_labels(&self) -> &[Label] {
        &self.test_labels
    }
}

/// ### Main function - Entry point for the font asset test.
fn main() {
    println!("🎯 Verturion Font Asset Test");
    println!("Testing ab_glyph modernization with fonts from assets directory");
    println!();

    let mut test = FontAssetTest::new();
    test.run_test();
}
