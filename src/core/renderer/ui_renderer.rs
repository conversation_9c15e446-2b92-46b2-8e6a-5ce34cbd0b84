//! Simple UI renderer with ab_glyph text rendering.
//!
//! This module provides a simplified UI rendering system that uses ab_glyph
//! for modern font handling while maintaining the baseline alignment fix.

use wgpu::{
    BindGroup, BindGroupDescriptor, BindGroupEntry, BindGroupLayoutDescriptor,
    BindGroupLayoutEntry, BindingType, Buffer, BufferDescriptor, BufferUsages,
    Device, RenderPipeline, RenderPipelineDescriptor, ShaderStages, TextureFormat,
    VertexAttribute, VertexBufferLayout, VertexFormat, VertexStepMode, Queue,
    RenderPass, Texture, TextureDescriptor, TextureDimension, TextureUsages,
    TextureView, Sampler, SamplerDescriptor, AddressMode, FilterMode,
};
use crate::core::math::{Vector2, Rect2};
use crate::core::variant::Color;
use ab_glyph::{Font<PERSON>rc, Font, ScaleFont};
use std::collections::HashMap;

/// ### Vertex data for UI rendering.
#[repr(C)]
#[derive(Copy, Clone, Debug, bytemuck::Pod, bytemuck::Zeroable)]
pub struct UIVertex {
    /// Position in screen space
    pub position: [f32; 2],
    /// Texture coordinates
    pub tex_coords: [f32; 2],
    /// Vertex color
    pub color: [f32; 4],
}

impl UIVertex {
    /// ### Creates a new UI vertex.
    pub fn new(position: Vector2, tex_coords: Vector2, color: Color) -> Self {
        Self {
            position: [position.x, position.y],
            tex_coords: [tex_coords.x, tex_coords.y],
            color: [color.r, color.g, color.b, color.a],
        }
    }

    /// ### Gets the vertex buffer layout descriptor.
    pub fn desc() -> VertexBufferLayout<'static> {
        VertexBufferLayout {
            array_stride: std::mem::size_of::<UIVertex>() as wgpu::BufferAddress,
            step_mode: VertexStepMode::Vertex,
            attributes: &[
                VertexAttribute {
                    offset: 0,
                    shader_location: 0,
                    format: VertexFormat::Float32x2,
                },
                VertexAttribute {
                    offset: std::mem::size_of::<[f32; 2]>() as wgpu::BufferAddress,
                    shader_location: 1,
                    format: VertexFormat::Float32x2,
                },
                VertexAttribute {
                    offset: std::mem::size_of::<[f32; 4]>() as wgpu::BufferAddress,
                    shader_location: 2,
                    format: VertexFormat::Float32x4,
                },
            ],
        }
    }
}

/// ### Cached glyph information for efficient text rendering.
#[derive(Debug, Clone)]
pub struct CachedGlyph {
    /// Character this glyph represents
    pub character: char,
    /// Font size used for this glyph
    pub font_size: u32,
    /// Position in texture atlas
    pub atlas_x: u32,
    pub atlas_y: u32,
    /// Glyph dimensions
    pub width: u32,
    pub height: u32,
    /// Bearing offsets for proper positioning
    pub bearing_x: i32,
    pub bearing_y: i32,
    /// Horizontal advance for next character
    pub advance: f32,
}

/// ### Font atlas for efficient GPU text rendering.
pub struct FontAtlas {
    /// GPU texture containing all cached glyphs
    pub texture: Texture,
    pub texture_view: TextureView,
    pub sampler: Sampler,
    /// Atlas dimensions
    pub width: u32,
    pub height: u32,
    /// Current packing position
    pub current_x: u32,
    pub current_y: u32,
    pub row_height: u32,
}

/// ### Simple UI renderer with ab_glyph text rendering.
pub struct UIRenderer {
    // Core rendering components
    pipeline: RenderPipeline,
    vertex_buffer: Buffer,
    index_buffer: Buffer,
    uniform_buffer: Buffer,
    uniform_bind_group: BindGroup,
    vertices: Vec<UIVertex>,
    indices: Vec<u16>,

    // Modern text rendering system using ab_glyph
    font: FontArc,
    font_atlas: FontAtlas,
    glyph_cache: HashMap<(char, u32), CachedGlyph>,
    pending_glyph_uploads: Vec<PendingGlyphUpload>,
    default_font_size: f32,
}

/// ### Pending glyph upload data for deferred GPU texture upload.
struct PendingGlyphUpload {
    atlas_x: u32,
    atlas_y: u32,
    width: u32,
    height: u32,
    bitmap: Vec<u8>,
}

impl UIRenderer {
    /// ### Creates a new UI renderer with ab_glyph text support.
    pub async fn new(
        device: &Device,
        _queue: &Queue,
        surface_format: TextureFormat,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        // Load default font (embedded or from system)
        let font_data = Self::load_default_font()?;

        // Create font atlas texture
        let atlas_size = 1024u32;
        let font_atlas = Self::create_font_atlas(device, atlas_size)?;

        // Create shader and pipeline for UI quads
        let shader = device.create_shader_module(wgpu::ShaderModuleDescriptor {
            label: Some("UI Shader"),
            source: wgpu::ShaderSource::Wgsl(include_str!("shaders/ui.wgsl").into()),
        });

        // Create uniform buffer for projection matrix
        let uniform_buffer = device.create_buffer(&BufferDescriptor {
            label: Some("UI Uniform Buffer"),
            size: 64, // 4x4 matrix
            usage: BufferUsages::UNIFORM | BufferUsages::COPY_DST,
            mapped_at_creation: false,
        });

        // Create bind group layout
        let bind_group_layout = device.create_bind_group_layout(&BindGroupLayoutDescriptor {
            label: Some("UI Bind Group Layout"),
            entries: &[
                BindGroupLayoutEntry {
                    binding: 0,
                    visibility: ShaderStages::VERTEX,
                    ty: BindingType::Buffer {
                        ty: wgpu::BufferBindingType::Uniform,
                        has_dynamic_offset: false,
                        min_binding_size: None,
                    },
                    count: None,
                },
                BindGroupLayoutEntry {
                    binding: 1,
                    visibility: ShaderStages::FRAGMENT,
                    ty: BindingType::Texture {
                        multisampled: false,
                        view_dimension: wgpu::TextureViewDimension::D2,
                        sample_type: wgpu::TextureSampleType::Float { filterable: true },
                    },
                    count: None,
                },
                BindGroupLayoutEntry {
                    binding: 2,
                    visibility: ShaderStages::FRAGMENT,
                    ty: BindingType::Sampler(wgpu::SamplerBindingType::Filtering),
                    count: None,
                },
            ],
        });

        // Create bind group
        let uniform_bind_group = device.create_bind_group(&BindGroupDescriptor {
            label: Some("UI Bind Group"),
            layout: &bind_group_layout,
            entries: &[
                BindGroupEntry {
                    binding: 0,
                    resource: uniform_buffer.as_entire_binding(),
                },
                BindGroupEntry {
                    binding: 1,
                    resource: wgpu::BindingResource::TextureView(&font_atlas.texture_view),
                },
                BindGroupEntry {
                    binding: 2,
                    resource: wgpu::BindingResource::Sampler(&font_atlas.sampler),
                },
            ],
        });

        // Create render pipeline
        let pipeline_layout = device.create_pipeline_layout(&wgpu::PipelineLayoutDescriptor {
            label: Some("UI Pipeline Layout"),
            bind_group_layouts: &[&bind_group_layout],
            push_constant_ranges: &[],
        });

        let pipeline = device.create_render_pipeline(&RenderPipelineDescriptor {
            label: Some("UI Pipeline"),
            layout: Some(&pipeline_layout),
            vertex: wgpu::VertexState {
                module: &shader,
                entry_point: "vs_main",
                buffers: &[UIVertex::desc()],
                compilation_options: wgpu::PipelineCompilationOptions::default(),
            },
            fragment: Some(wgpu::FragmentState {
                module: &shader,
                entry_point: "fs_main",
                targets: &[Some(wgpu::ColorTargetState {
                    format: surface_format,
                    blend: Some(wgpu::BlendState::ALPHA_BLENDING),
                    write_mask: wgpu::ColorWrites::ALL,
                })],
                compilation_options: wgpu::PipelineCompilationOptions::default(),
            }),
            primitive: wgpu::PrimitiveState {
                topology: wgpu::PrimitiveTopology::TriangleList,
                strip_index_format: None,
                front_face: wgpu::FrontFace::Ccw,
                cull_mode: None,
                unclipped_depth: false,
                polygon_mode: wgpu::PolygonMode::Fill,
                conservative: false,
            },
            depth_stencil: None,
            multisample: wgpu::MultisampleState {
                count: 1,
                mask: !0,
                alpha_to_coverage_enabled: false,
            },
            multiview: None,
            cache: None,
        });

        // Create vertex and index buffers
        let vertex_buffer = device.create_buffer(&BufferDescriptor {
            label: Some("UI Vertex Buffer"),
            size: 65536, // 64KB initial size
            usage: BufferUsages::VERTEX | BufferUsages::COPY_DST,
            mapped_at_creation: false,
        });

        let index_buffer = device.create_buffer(&BufferDescriptor {
            label: Some("UI Index Buffer"),
            size: 32768, // 32KB initial size
            usage: BufferUsages::INDEX | BufferUsages::COPY_DST,
            mapped_at_creation: false,
        });

        // Create FontArc from the font data (this owns the data)
        let font = FontArc::try_from_vec(font_data)?;

        Ok(Self {
            pipeline,
            vertex_buffer,
            index_buffer,
            uniform_buffer,
            uniform_bind_group,
            vertices: Vec::new(),
            indices: Vec::new(),
            font,
            font_atlas,
            glyph_cache: HashMap::new(),
            pending_glyph_uploads: Vec::new(),
            default_font_size: 16.0,
        })
    }

    /// ### Loads the default embedded font.
    fn load_default_font() -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        // Try different fonts from assets directory for comprehensive testing
        let font_candidates = [
            "assets/fonts/Roboto-Regular.ttf",
            "assets/fonts/DejaVuSansMono.ttf",
            "assets/fonts/Coolvetica Rg.otf",
            "assets/fonts/Roboto-Bold.ttf",
            "assets/fonts/default.ttf",
        ];

        for font_path in &font_candidates {
            if let Ok(font_data) = std::fs::read(font_path) {
                println!("UIRenderer: Loaded font from {}", font_path);
                return Ok(font_data);
            }
        }

        println!("UIRenderer: No fonts found in assets directory, trying system fonts...");

        // Try to load a system font as fallback
        #[cfg(target_os = "linux")]
        {
            let font_paths = [
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                "/usr/share/fonts/TTF/DejaVuSans.ttf",
                "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                "/usr/share/fonts/TTF/LiberationSans-Regular.ttf",
            ];

            for path in &font_paths {
                if let Ok(font_data) = std::fs::read(path) {
                    println!("UIRenderer: Loaded system font from {}", path);
                    return Ok(font_data);
                }
            }
        }

        // If no system font found, provide helpful error message
        Err(format!(
            "No suitable font found. Please:\n\
            1. Add a TTF font file to assets/fonts/\n\
            2. Install system fonts (DejaVu, Liberation on Linux)\n\
            3. Ensure font files are readable by the application"
        ).into())
    }

    /// ### Creates a font atlas texture for GPU rendering.
    fn create_font_atlas(device: &Device, size: u32) -> Result<FontAtlas, Box<dyn std::error::Error>> {
        let texture = device.create_texture(&TextureDescriptor {
            label: Some("Font Atlas"),
            size: wgpu::Extent3d {
                width: size,
                height: size,
                depth_or_array_layers: 1,
            },
            mip_level_count: 1,
            sample_count: 1,
            dimension: TextureDimension::D2,
            format: TextureFormat::R8Unorm,
            usage: TextureUsages::TEXTURE_BINDING | TextureUsages::COPY_DST,
            view_formats: &[],
        });

        let texture_view = texture.create_view(&wgpu::TextureViewDescriptor::default());

        let sampler = device.create_sampler(&SamplerDescriptor {
            label: Some("Font Atlas Sampler - Hybrid Anti-aliasing"),
            address_mode_u: AddressMode::ClampToEdge,
            address_mode_v: AddressMode::ClampToEdge,
            address_mode_w: AddressMode::ClampToEdge,
            // Use linear filtering for magnification to enable shader-based anti-aliasing
            // while maintaining nearest for minification to preserve sharpness
            mag_filter: FilterMode::Linear,
            min_filter: FilterMode::Nearest,
            mipmap_filter: FilterMode::Nearest,
            ..Default::default()
        });

        Ok(FontAtlas {
            texture,
            texture_view,
            sampler,
            width: size,
            height: size,
            current_x: 0,
            current_y: 0,
            row_height: 0,
        })
    }

    /// ### Adds a quad to the vertex buffer.
    pub fn add_quad(&mut self, rect: Rect2, color: Color) {
        let base_index = self.vertices.len() as u16;

        // Create four vertices for the quad with special texture coordinates for solid color
        // Use negative texture coordinates to signal solid color rendering
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x, rect.position.y),
            Vector2::new(-1.0, -1.0),
            color,
        ));
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x + rect.size.x, rect.position.y),
            Vector2::new(-1.0, -1.0),
            color,
        ));
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x + rect.size.x, rect.position.y + rect.size.y),
            Vector2::new(-1.0, -1.0),
            color,
        ));
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x, rect.position.y + rect.size.y),
            Vector2::new(-1.0, -1.0),
            color,
        ));

        // Add indices for two triangles
        self.indices.extend_from_slice(&[
            base_index, base_index + 1, base_index + 2,
            base_index, base_index + 2, base_index + 3,
        ]);
    }

    /// ### Adds a textured quad to the render queue.
    fn add_textured_quad(&mut self, rect: Rect2, tex_coords: Rect2, color: Color) {
        let base_index = self.vertices.len() as u16;

        // Add vertices for the textured quad
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x, rect.position.y),
            Vector2::new(tex_coords.position.x, tex_coords.position.y),
            color
        ));
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x + rect.size.x, rect.position.y),
            Vector2::new(tex_coords.position.x + tex_coords.size.x, tex_coords.position.y),
            color
        ));
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x + rect.size.x, rect.position.y + rect.size.y),
            Vector2::new(tex_coords.position.x + tex_coords.size.x, tex_coords.position.y + tex_coords.size.y),
            color
        ));
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x, rect.position.y + rect.size.y),
            Vector2::new(tex_coords.position.x, tex_coords.position.y + tex_coords.size.y),
            color
        ));

        // Add indices for two triangles
        self.indices.extend_from_slice(&[
            base_index, base_index + 1, base_index + 2,
            base_index, base_index + 2, base_index + 3,
        ]);
    }

    /// ### Gets or caches a glyph for the specified character and font size.
    fn get_or_cache_glyph(&mut self, character: char, font_size: u32) -> Result<CachedGlyph, Box<dyn std::error::Error>> {
        let cache_key = (character, font_size);

        // Check if glyph is already cached
        if !self.glyph_cache.contains_key(&cache_key) {
            // Create a local copy of the font to avoid borrowing issues
            let font_clone = self.font.clone();
            let scaled_font = font_clone.as_scaled(font_size as f32);
            let glyph_id = scaled_font.glyph_id(character);

            // Check if the font contains this character
            if glyph_id.0 == 0 && character != '\0' {
                // Font doesn't contain this character, return error
                return Err(format!("Font does not contain character '{}'", character).into());
            }

            let glyph = glyph_id.with_scale_and_position(font_size as f32, ab_glyph::point(0.0, 0.0));

            if let Some(outlined) = scaled_font.outline_glyph(glyph) {
                let bounds = outlined.px_bounds();
                let width = bounds.width() as u32;
                let height = bounds.height() as u32;
                let advance = scaled_font.h_advance(glyph_id);

                if width > 0 && height > 0 {
                    // Add padding to prevent bleeding artifacts between glyphs
                    let padding = 2u32; // Increased padding for better anti-aliasing
                    let padded_width = width + 2 * padding;
                    let padded_height = height + 2 * padding;

                    // Find space in the atlas for padded glyph
                    let (atlas_x, atlas_y) = self.find_atlas_space(padded_width, padded_height)?;

                    // High-quality glyph rasterization with supersampling
                    let supersample_factor = 2; // 2x supersampling for better quality
                    let supersample_width = padded_width * supersample_factor;
                    let supersample_height = padded_height * supersample_factor;

                    // Create supersampled bitmap
                    let mut supersample_bitmap = vec![0.0f32; (supersample_width * supersample_height) as usize];

                    // Rasterize at higher resolution for better anti-aliasing
                    let supersample_glyph = glyph_id.with_scale_and_position(
                        font_size as f32 * supersample_factor as f32,
                        ab_glyph::point(padding as f32 * supersample_factor as f32, padding as f32 * supersample_factor as f32)
                    );

                    if let Some(supersample_outlined) = scaled_font.outline_glyph(supersample_glyph) {
                        supersample_outlined.draw(|x, y, coverage| {
                            if x < supersample_width && y < supersample_height {
                                let index = (y * supersample_width + x) as usize;
                                // High-precision coverage for supersampling
                                supersample_bitmap[index] = coverage.clamp(0.0, 1.0);
                            }
                        });
                    }

                    // Downsample with high-quality filtering for final bitmap
                    let mut bitmap = vec![0u8; (padded_width * padded_height) as usize];
                    Self::downsample_with_antialiasing_static(
                        &supersample_bitmap,
                        supersample_width,
                        supersample_height,
                        &mut bitmap,
                        padded_width,
                        padded_height,
                        supersample_factor
                    );

                    // Store bitmap for later GPU upload
                    self.pending_glyph_uploads.push(PendingGlyphUpload {
                        atlas_x,
                        atlas_y,
                        width: padded_width,
                        height: padded_height,
                        bitmap,
                    });

                    // Cache the glyph with proper baseline metrics and padding info
                    let cached_glyph = CachedGlyph {
                        character,
                        font_size,
                        // Store atlas position offset by padding for texture coordinates
                        atlas_x: atlas_x + padding,
                        atlas_y: atlas_y + padding,
                        // Store original glyph dimensions (without padding)
                        width,
                        height,
                        bearing_x: bounds.min.x as i32,
                        // bearing_y should be distance from baseline to bottom of glyph
                        bearing_y: bounds.min.y as i32,
                        advance,
                    };

                    self.glyph_cache.insert(cache_key, cached_glyph);
                }
            }
        }

        self.glyph_cache.get(&cache_key)
            .cloned()
            .ok_or_else(|| format!("Failed to cache glyph for character '{}'", character).into())
    }

    /// ### Finds space in the font atlas for a glyph of the given dimensions.
    fn find_atlas_space(&mut self, width: u32, height: u32) -> Result<(u32, u32), Box<dyn std::error::Error>> {
        // Simple row-based packing algorithm
        if self.font_atlas.current_x + width > self.font_atlas.width {
            // Move to next row
            self.font_atlas.current_x = 0;
            self.font_atlas.current_y += self.font_atlas.row_height;
            self.font_atlas.row_height = 0;
        }

        if self.font_atlas.current_y + height > self.font_atlas.height {
            return Err("Font atlas is full".into());
        }

        let atlas_x = self.font_atlas.current_x;
        let atlas_y = self.font_atlas.current_y;

        // Update atlas state
        self.font_atlas.current_x += width;
        self.font_atlas.row_height = self.font_atlas.row_height.max(height);

        Ok((atlas_x, atlas_y))
    }

    /// ### High-quality downsampling with anti-aliasing for supersampled glyphs.
    ///
    /// Uses a box filter with gamma-correct blending to downsample supersampled
    /// glyph bitmaps while preserving smooth edges and preventing aliasing artifacts.
    fn downsample_with_antialiasing_static(
        supersample_bitmap: &[f32],
        supersample_width: u32,
        supersample_height: u32,
        output_bitmap: &mut [u8],
        output_width: u32,
        output_height: u32,
        supersample_factor: u32,
    ) {
        let samples_per_pixel = supersample_factor * supersample_factor;

        for y in 0..output_height {
            for x in 0..output_width {
                let mut sum = 0.0f32;

                // Sample all supersampled pixels that contribute to this output pixel
                for sy in 0..supersample_factor {
                    for sx in 0..supersample_factor {
                        let sample_x = x * supersample_factor + sx;
                        let sample_y = y * supersample_factor + sy;

                        if sample_x < supersample_width && sample_y < supersample_height {
                            let sample_index = (sample_y * supersample_width + sample_x) as usize;
                            let sample_value = supersample_bitmap[sample_index];

                            // Gamma-correct blending for better anti-aliasing
                            sum += sample_value * sample_value; // Square for gamma correction
                        }
                    }
                }

                // Average and apply inverse gamma correction
                let avg = sum / samples_per_pixel as f32;
                let gamma_corrected = avg.sqrt().clamp(0.0, 1.0);

                // Convert to 8-bit with proper rounding
                let output_index = (y * output_width + x) as usize;
                output_bitmap[output_index] = (gamma_corrected * 255.0).round() as u8;
            }
        }
    }

    /// ### Loads a new font from the specified path.
    pub fn load_font(&mut self, font_path: &str) -> Result<(), Box<dyn std::error::Error>> {
        println!("UIRenderer: Loading font from {}", font_path);

        // Load font data from file
        let font_data = std::fs::read(font_path)
            .map_err(|e| format!("Failed to read font file {}: {}", font_path, e))?;

        // Create FontArc from the font data
        let new_font = FontArc::try_from_vec(font_data)
            .map_err(|e| format!("Failed to parse font {}: {}", font_path, e))?;

        // Clear existing glyph cache since we're switching fonts
        self.glyph_cache.clear();
        self.pending_glyph_uploads.clear();

        // Reset font atlas packing state
        self.font_atlas.current_x = 0;
        self.font_atlas.current_y = 0;
        self.font_atlas.row_height = 0;

        // Update the current font
        self.font = new_font;

        println!("UIRenderer: Successfully loaded font: {}", font_path);
        Ok(())
    }

    /// ### Renders text at the specified position using proper typographic baseline alignment.
    ///
    /// This method implements correct baseline alignment where:
    /// - The baseline is the reference line for text positioning
    /// - Ascenders (like 'h', 'b', 'd') extend above the baseline
    /// - Descenders (like 'g', 'p', 'q', 'j', 'y') extend below the baseline
    /// - All characters align consistently on the baseline regardless of their individual heights
    pub fn render_text(
        &mut self,
        text: &str,
        position: Vector2,
        color: Color,
        font_size: f32,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Use default font size if none specified
        let effective_font_size = if font_size > 0.0 { font_size } else { self.default_font_size };

        println!("UIRenderer: Rendering text '{}' at ({:.1}, {:.1}) with size {:.1}",
            text, position.x, position.y, effective_font_size);

        // Render actual text glyphs using ab_glyph
        let mut current_x = position.x;
        let font_size_u32 = effective_font_size as u32;

        // Calculate proper baseline reference point
        // The position.y represents the top of the text bounding box
        // We need to add the ascent to get to the baseline reference
        let font_metrics = self.font.as_scaled(effective_font_size);
        let baseline_y = position.y + font_metrics.ascent();

        for character in text.chars() {
            // Skip whitespace characters but advance position
            if character.is_whitespace() {
                if character == ' ' {
                    current_x += effective_font_size * 0.25; // Space width
                }
                continue;
            }

            // Get or cache the glyph, skip if not available
            let glyph = match self.get_or_cache_glyph(character, font_size_u32) {
                Ok(glyph) => glyph,
                Err(e) => {
                    println!("Warning: Skipping character '{}': {}", character, e);
                    current_x += effective_font_size * 0.5; // Approximate character width
                    continue;
                }
            };

            // Calculate proper glyph position with subpixel positioning support
            let glyph_x = current_x + glyph.bearing_x as f32;
            let glyph_y = baseline_y + glyph.bearing_y as f32;

            // Implement subpixel positioning for improved text quality
            // Round to nearest quarter-pixel for better spacing and reduced artifacts
            let subpixel_x = (glyph_x * 4.0).round() / 4.0;
            let subpixel_y = (glyph_y * 4.0).round() / 4.0;

            // Create texture coordinates for the glyph in the atlas
            let tex_coords = Rect2::new(
                glyph.atlas_x as f32 / self.font_atlas.width as f32,
                glyph.atlas_y as f32 / self.font_atlas.height as f32,
                glyph.width as f32 / self.font_atlas.width as f32,
                glyph.height as f32 / self.font_atlas.height as f32,
            );

            // Add glyph quad with subpixel positioning for improved quality
            self.add_textured_quad(
                Rect2::from_position_size(
                    Vector2::new(subpixel_x, subpixel_y),
                    Vector2::new(glyph.width as f32, glyph.height as f32)
                ),
                tex_coords,
                color
            );

            // Advance to next character position
            current_x += glyph.advance;
        }

        Ok(())
    }

    /// ### Clears all queued rendering data.
    pub fn clear(&mut self) {
        self.vertices.clear();
        self.indices.clear();
    }

    /// ### Flushes all rendering commands to the GPU.
    pub fn flush(
        &mut self,
        render_pass: &mut RenderPass,
        queue: &Queue,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Upload any pending glyph data to the font atlas
        for upload in &self.pending_glyph_uploads {
            queue.write_texture(
                wgpu::ImageCopyTexture {
                    texture: &self.font_atlas.texture,
                    mip_level: 0,
                    origin: wgpu::Origin3d {
                        x: upload.atlas_x,
                        y: upload.atlas_y,
                        z: 0,
                    },
                    aspect: wgpu::TextureAspect::All,
                },
                &upload.bitmap,
                wgpu::ImageDataLayout {
                    offset: 0,
                    bytes_per_row: Some(upload.width),
                    rows_per_image: Some(upload.height),
                },
                wgpu::Extent3d {
                    width: upload.width,
                    height: upload.height,
                    depth_or_array_layers: 1,
                },
            );
        }
        self.pending_glyph_uploads.clear();

        // Update projection matrix
        let projection = Self::create_projection_matrix(window_size);
        queue.write_buffer(&self.uniform_buffer, 0, bytemuck::cast_slice(&projection));

        // Upload vertex and index data if we have any quads
        if !self.vertices.is_empty() {
            queue.write_buffer(&self.vertex_buffer, 0, bytemuck::cast_slice(&self.vertices));
            queue.write_buffer(&self.index_buffer, 0, bytemuck::cast_slice(&self.indices));

            // Render UI quads
            render_pass.set_pipeline(&self.pipeline);
            render_pass.set_bind_group(0, &self.uniform_bind_group, &[]);
            render_pass.set_vertex_buffer(0, self.vertex_buffer.slice(..));
            render_pass.set_index_buffer(self.index_buffer.slice(..), wgpu::IndexFormat::Uint16);
            render_pass.draw_indexed(0..self.indices.len() as u32, 0, 0..1);
        }

        Ok(())
    }

    /// ### Creates an orthographic projection matrix for UI rendering.
    fn create_projection_matrix(window_size: Vector2) -> [[f32; 4]; 4] {
        let left = 0.0;
        let right = window_size.x;
        let bottom = window_size.y;
        let top = 0.0;
        let near = -1.0;
        let far = 1.0;

        [
            [2.0 / (right - left), 0.0, 0.0, 0.0],
            [0.0, 2.0 / (top - bottom), 0.0, 0.0],
            [0.0, 0.0, -2.0 / (far - near), 0.0],
            [-(right + left) / (right - left), -(top + bottom) / (top - bottom), -(far + near) / (far - near), 1.0],
        ]
    }
}
