// UI rendering shader for Verturion graphics system

struct Uniforms {
    projection: mat4x4<f32>,
}

@group(0) @binding(0)
var<uniform> uniforms: Uniforms;

@group(0) @binding(1)
var font_texture: texture_2d<f32>;

@group(0) @binding(2)
var font_sampler: sampler;

struct VertexInput {
    @location(0) position: vec2<f32>,
    @location(1) tex_coords: vec2<f32>,
    @location(2) color: vec4<f32>,
}

struct VertexOutput {
    @builtin(position) clip_position: vec4<f32>,
    @location(0) tex_coords: vec2<f32>,
    @location(1) color: vec4<f32>,
}

@vertex
fn vs_main(input: VertexInput) -> VertexOutput {
    var out: VertexOutput;

    // Transform position to clip space
    out.clip_position = uniforms.projection * vec4<f32>(input.position, 0.0, 1.0);
    out.tex_coords = input.tex_coords;
    out.color = input.color;

    return out;
}

@fragment
fn fs_main(in: VertexOutput) -> @location(0) vec4<f32> {
    // Check if this is a solid color quad (negative texture coordinates)
    if (in.tex_coords.x < 0.0 || in.tex_coords.y < 0.0) {
        // Solid color quad (UI elements like buttons, backgrounds)
        return in.color;
    }

    // Advanced anti-aliased text rendering with subpixel sampling
    let alpha = advanced_text_antialiasing(in.tex_coords);

    // Modulate color with anti-aliased alpha for smooth text glyphs
    return vec4<f32>(in.color.rgb, in.color.a * alpha);
}

/// ### Advanced anti-aliasing function for high-quality text rendering
///
/// Implements multi-sample anti-aliasing with subpixel positioning and
/// selective smoothing to eliminate jagged edges while maintaining sharpness.
fn advanced_text_antialiasing(tex_coords: vec2<f32>) -> f32 {
    // Get texture dimensions for subpixel sampling
    let texture_size = textureDimensions(font_texture);
    let texel_size = 1.0 / vec2<f32>(texture_size);

    // Sample the center pixel
    let center_alpha = textureSample(font_texture, font_sampler, tex_coords).r;

    // Early exit for fully opaque or transparent pixels (performance optimization)
    if (center_alpha >= 0.99) {
        return pow(center_alpha, 1.0 / 2.2); // Gamma correction
    }
    if (center_alpha <= 0.01) {
        return 0.0;
    }

    // Multi-sample anti-aliasing with 4x subpixel sampling
    // Sample at quarter-pixel offsets for smoother edges
    let offset = texel_size * 0.25;

    let sample1 = textureSample(font_texture, font_sampler, tex_coords + vec2<f32>(-offset.x, -offset.y)).r;
    let sample2 = textureSample(font_texture, font_sampler, tex_coords + vec2<f32>(offset.x, -offset.y)).r;
    let sample3 = textureSample(font_texture, font_sampler, tex_coords + vec2<f32>(-offset.x, offset.y)).r;
    let sample4 = textureSample(font_texture, font_sampler, tex_coords + vec2<f32>(offset.x, offset.y)).r;

    // Calculate average with weighted center sample for better quality
    let avg_alpha = (center_alpha * 2.0 + sample1 + sample2 + sample3 + sample4) / 6.0;

    // Apply edge detection for selective smoothing
    let edge_strength = abs(sample1 - sample3) + abs(sample2 - sample4);

    // Blend between sharp and smooth based on edge strength
    let smooth_factor = clamp(edge_strength * 2.0, 0.0, 1.0);
    let final_alpha = mix(center_alpha, avg_alpha, smooth_factor);

    // Apply gamma correction for proper text appearance
    return pow(clamp(final_alpha, 0.0, 1.0), 1.0 / 2.2);
}
